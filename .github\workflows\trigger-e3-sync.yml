# This workflow triggers sync when E3-related files are modified

name: Trigger E3 Tools Sync

on:
  push:
    branches: [ master, main ]
    paths:
      # Legacy individual scripts
      - 'apps/set_wire_numbers.py'
      - 'apps/test_unique_wire_numbers.py'
      # E3 connection manager test files
      - 'apps/test_e3_selector_gui.py'
      # New library modules
      - 'lib/e3_wire_numbering.py'
      - 'lib/e3_device_designation.py'
      - 'lib/e3_terminal_pin_names.py'
      - 'lib/e3_connection_manager.py'
      - 'lib/theme_utils.py'
      # NA Standards GUI
      - 'apps/e3_NA_Standards.py'
      # Documentation
      - 'README_wire_numbering.md'
      - 'README_device_designation.md'
      - 'README_terminal_pin_names.md'
      - 'README_e3_connection_manager.md'
      # Dependencies
      - 'requirements.txt'

jobs:
  trigger-sync:
    runs-on: ubuntu-latest
    
    steps:
    - name: Trigger Sync in E3 Automation Repository
      uses: peter-evans/repository-dispatch@v2
      with:
        token: ${{ secrets.E3_REPO_TOKEN }}
        repository: nahallac/e3-automation-tools
        event-type: sync-e3-tools
        client-payload: |
          {
            "ref": "${{ github.ref }}",
            "sha": "${{ github.sha }}",
            "changed_files": "${{ github.event.head_commit.modified }}"
          }
